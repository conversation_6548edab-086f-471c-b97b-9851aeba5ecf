import Foundation
import Combine

// MARK: - API配置
struct APIConfig {
    static let baseURL = "http://localhost:8000/api/v1"
    static var authToken: String? {
        get {
            UserDefaults.standard.string(forKey: "auth_token")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "auth_token")
        }
    }
}

// MARK: - API错误类型
enum APIError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError
    case networkError(String)
    case serverError(Int, String)
    case unauthorized
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        case .decodingError:
            return "数据解析错误"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .serverError(let code, let message):
            return "服务器错误 (\(code)): \(message)"
        case .unauthorized:
            return "未授权访问"
        }
    }
}

// MARK: - HTTP方法
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - API服务
class APIService {
    static let shared = APIService()
    private let session = URLSession.shared
    
    private init() {}
    
    // MARK: - 通用请求方法
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        body: Data? = nil,
        responseType: T.Type
    ) -> AnyPublisher<T, APIError> {
        
        guard let url = URL(string: APIConfig.baseURL + endpoint) else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证头
        if let token = APIConfig.authToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        if let body = body {
            request.httpBody = body
        }
        
        return session.dataTaskPublisher(for: request)
            .map(\.data)
            .decode(type: T.self, decoder: JSONDecoder())
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else {
                    return APIError.networkError(error.localizedDescription)
                }
            }
            .eraseToAnyPublisher()
    }
    
    // MARK: - 健康检查
    func healthCheck() -> AnyPublisher<[String: String], APIError> {
        return request(
            endpoint: "/auth/health",
            method: .GET,
            responseType: [String: String].self
        )
    }
}

// MARK: - 宠物API服务
class PetAPIService {
    static let shared = PetAPIService()
    private let apiService = APIService.shared
    
    private init() {}
    
    // 获取用户的宠物列表
    func getUserPets(page: Int = 1, size: Int = 10) -> AnyPublisher<PetListResponse, APIError> {
        return apiService.request(
            endpoint: "/pets?page=\(page)&size=\(size)",
            method: .GET,
            responseType: PetListResponse.self
        )
    }
    
    // 创建新宠物
    func createPet(_ petData: PetCreateRequest) -> AnyPublisher<Pet, APIError> {
        guard let body = try? JSONEncoder().encode(petData) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        return apiService.request(
            endpoint: "/pets",
            method: .POST,
            body: body,
            responseType: Pet.self
        )
    }
    
    // 更新宠物信息
    func updatePet(id: Int, petData: PetUpdateRequest) -> AnyPublisher<Pet, APIError> {
        guard let body = try? JSONEncoder().encode(petData) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        return apiService.request(
            endpoint: "/pets/\(id)",
            method: .PUT,
            body: body,
            responseType: Pet.self
        )
    }
    
    // 删除宠物
    func deletePet(id: Int) -> AnyPublisher<MessageResponse, APIError> {
        return apiService.request(
            endpoint: "/pets/\(id)",
            method: .DELETE,
            responseType: MessageResponse.self
        )
    }
}

// MARK: - 动态API服务
class FeedAPIService {
    static let shared = FeedAPIService()
    private let apiService = APIService.shared
    
    private init() {}
    
    // 获取动态列表
    func getFeeds(page: Int = 1, size: Int = 10) -> AnyPublisher<FeedListResponse, APIError> {
        return apiService.request(
            endpoint: "/feeds?page=\(page)&size=\(size)",
            method: .GET,
            responseType: FeedListResponse.self
        )
    }
    
    // 创建动态
    func createFeed(_ feedData: FeedCreateRequest) -> AnyPublisher<Feed, APIError> {
        guard let body = try? JSONEncoder().encode(feedData) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        return apiService.request(
            endpoint: "/feeds",
            method: .POST,
            body: body,
            responseType: Feed.self
        )
    }
    
    // 点赞动态
    func likeFeed(id: Int) -> AnyPublisher<MessageResponse, APIError> {
        return apiService.request(
            endpoint: "/feeds/\(id)/like",
            method: .POST,
            responseType: MessageResponse.self
        )
    }
    
    // 取消点赞
    func unlikeFeed(id: Int) -> AnyPublisher<MessageResponse, APIError> {
        return apiService.request(
            endpoint: "/feeds/\(id)/unlike",
            method: .POST,
            responseType: MessageResponse.self
        )
    }
}

// MARK: - AI聊天API服务
class ChatAPIService {
    static let shared = ChatAPIService()
    private let apiService = APIService.shared
    
    private init() {}
    
    // 发送聊天消息
    func sendMessage(_ request: ChatRequest) -> AnyPublisher<ChatResponse, APIError> {
        guard let body = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        return apiService.request(
            endpoint: "/ai/chat",
            method: .POST,
            body: body,
            responseType: ChatResponse.self
        )
    }
    
    // 获取对话历史
    func getConversationHistory(petId: Int, limit: Int = 20, offset: Int = 0) -> AnyPublisher<ConversationHistoryResponse, APIError> {
        return apiService.request(
            endpoint: "/ai/conversations/\(petId)/history?limit=\(limit)&offset=\(offset)",
            method: .GET,
            responseType: ConversationHistoryResponse.self
        )
    }
}

// MARK: - 响应模型
struct MessageResponse: Codable {
    let message: String
    let success: Bool?
}

struct PetListResponse: Codable {
    let pets: [Pet]
    let total: Int
    let page: Int
    let size: Int
    let hasMore: Bool
}

struct FeedListResponse: Codable {
    let feeds: [Feed]
    let total: Int
    let page: Int
    let size: Int
    let hasMore: Bool
}

// MARK: - 请求模型
struct PetCreateRequest: Codable {
    let name: String
    let breed: String
    let age: Int?
    let gender: String?
    let color: String?
    let personality: String?
    let personalityTags: [String]?
    let currentMood: String?
    let responseStyle: String?
}

struct PetUpdateRequest: Codable {
    let name: String?
    let breed: String?
    let age: Int?
    let gender: String?
    let color: String?
    let personality: String?
    let personalityTags: [String]?
    let currentMood: String?
    let responseStyle: String?
}

struct FeedCreateRequest: Codable {
    let content: String
    let petId: Int
    let images: [String]?
    let location: String?
    let mood: String?
}

struct ChatRequest: Codable {
    let message: String
    let petId: Int
    let contextData: [String: String]?
}

struct ChatResponse: Codable {
    let reply: String
    let mood: String
    let actions: [String]
    let emotions: [String]
    let confidence: Double
    let conversationId: Int
    let messageId: Int
    let responseTime: Double
    let tokensUsed: Int
}

struct ConversationHistoryResponse: Codable {
    let conversationId: Int
    let messages: [ChatMessage]
    let totalCount: Int
    let hasMore: Bool
}

struct ChatMessage: Codable {
    let id: Int
    let content: String
    let type: String
    let mood: String?
    let actions: [String]?
    let emotions: [String]?
    let createdAt: String
    let confidence: Double?
    let tokensUsed: Int?
}
