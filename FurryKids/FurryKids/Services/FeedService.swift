import Foundation
import Combine

// MARK: - Helper Response Types
struct EmptyResponse: Codable {
    // 用于处理没有返回数据的API响应
}

// MARK: - Feed Service Protocol
protocol FeedServiceProtocol {
    // Feed CRUD
    func getFeeds(page: Int, limit: Int) -> AnyPublisher<[Feed], APIError>
    func getUserFeeds(userId: Int, page: Int, limit: Int) -> AnyPublisher<[Feed], APIError>
    func getPetFeeds(petId: Int, page: Int, limit: Int) -> AnyPublisher<[Feed], APIError>
    func createFeed(_ request: FeedCreateRequest) -> AnyPublisher<Feed, APIError>
    func updateFeed(feedId: Int, request: FeedUpdateRequest) -> AnyPublisher<Feed, APIError>
    func deleteFeed(feedId: Int) -> AnyPublisher<Bool, APIError>

    // Feed Interactions
    func likeFeed(feedId: Int) -> AnyPublisher<FeedLikeResponse, APIError>
    func unlikeFeed(feedId: Int) -> AnyPublisher<Bool, APIError>
    func shareFeed(feedId: Int) -> AnyPublisher<Bool, APIError>

    // Comments
    func getFeedComments(feedId: Int, page: Int, limit: Int) -> AnyPublisher<[FeedComment], APIError>
    func addComment(feedId: Int, content: String, parentId: Int?) -> AnyPublisher<FeedComment, APIError>
    func deleteComment(commentId: Int) -> AnyPublisher<Bool, APIError>
}

// MARK: - Response Models
struct FeedLikeResponse: Codable {
    let feedId: Int
    let isLiked: Bool
    let likesCount: Int

    enum CodingKeys: String, CodingKey {
        case feedId = "feed_id"
        case isLiked = "is_liked"
        case likesCount = "likes_count"
    }
}

struct FeedListResponse: Codable {
    let feeds: [Feed]
    let total: Int
    let page: Int
    let size: Int
    let hasMore: Bool

    enum CodingKeys: String, CodingKey {
        case feeds, total, page, size
        case hasMore = "has_more"
    }
}

// MARK: - Feed Service Implementation
class FeedService: FeedServiceProtocol {
    static let shared = FeedService()
    
    private let networkManager = NetworkManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Local cache
    @Published var cachedFeeds: [Feed] = []
    @Published var isLoading = false
    
    private init() {}
    
    // MARK: - Feed CRUD Operations
    func getFeeds(page: Int = 1, limit: Int = 20) -> AnyPublisher<[Feed], APIError> {
        isLoading = true

        return networkManager.request(
            endpoint: "/feeds?page=\(page)&size=\(limit)",
            method: .GET,
            responseType: FeedListResponse.self
        )
        .map { response in
            return response.feeds
        }
        .handleEvents(
            receiveOutput: { [weak self] feeds in
                if page == 1 {
                    self?.cachedFeeds = feeds
                } else {
                    self?.cachedFeeds.append(contentsOf: feeds)
                }
                self?.isLoading = false
            },
            receiveCompletion: { [weak self] _ in
                self?.isLoading = false
            }
        )
        .eraseToAnyPublisher()
    }
    
    func getUserFeeds(userId: Int, page: Int = 1, limit: Int = 20) -> AnyPublisher<[Feed], APIError> {
        return networkManager.request(
            endpoint: "/feeds?user_id=\(userId)&page=\(page)&size=\(limit)",
            method: .GET,
            responseType: FeedListResponse.self
        )
        .map { response in
            return response.feeds
        }
        .eraseToAnyPublisher()
    }

    func getPetFeeds(petId: Int, page: Int = 1, limit: Int = 20) -> AnyPublisher<[Feed], APIError> {
        return networkManager.request(
            endpoint: "/feeds?pet_id=\(petId)&page=\(page)&size=\(limit)",
            method: .GET,
            responseType: FeedListResponse.self
        )
        .map { response in
            return response.feeds
        }
        .eraseToAnyPublisher()
    }
    
    func createFeed(_ request: FeedCreateRequest) -> AnyPublisher<Feed, APIError> {
        guard let requestData = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        return networkManager.request(
            endpoint: "/feeds",
            method: .POST,
            body: requestData,
            responseType: Feed.self
        )
        .handleEvents(receiveOutput: { [weak self] newFeed in
            self?.cachedFeeds.insert(newFeed, at: 0)
        })
        .eraseToAnyPublisher()
    }
    
    func updateFeed(feedId: Int, request: FeedUpdateRequest) -> AnyPublisher<Feed, APIError> {
        guard let requestData = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        return networkManager.request(
            endpoint: "/feeds/\(feedId)",
            method: .PUT,
            body: requestData,
            responseType: Feed.self
        )
        .handleEvents(receiveOutput: { [weak self] updatedFeed in
            if let index = self?.cachedFeeds.firstIndex(where: { $0.id == feedId }) {
                self?.cachedFeeds[index] = updatedFeed
            }
        })
        .eraseToAnyPublisher()
    }
    
    func deleteFeed(feedId: Int) -> AnyPublisher<Bool, APIError> {
        return networkManager.request(
            endpoint: "/feeds/\(feedId)",
            method: .DELETE,
            responseType: EmptyResponse.self
        )
        .map { _ in
            return true
        }
        .handleEvents(receiveOutput: { [weak self] success in
            if success {
                self?.cachedFeeds.removeAll { $0.id == feedId }
            }
        })
        .eraseToAnyPublisher()
    }
    
    // MARK: - Feed Interactions
    func likeFeed(feedId: Int) -> AnyPublisher<FeedLikeResponse, APIError> {
        return networkManager.request(
            endpoint: "/feeds/\(feedId)/like",
            method: .POST,
            responseType: FeedLikeResponse.self
        )
        .handleEvents(receiveOutput: { [weak self] response in
            // 更新本地缓存
            if let index = self?.cachedFeeds.firstIndex(where: { $0.id == feedId }) {
                var updatedFeed = self?.cachedFeeds[index]
                updatedFeed?.isLiked = response.isLiked
                updatedFeed?.likesCount = response.likesCount
                if let feed = updatedFeed {
                    self?.cachedFeeds[index] = feed
                }
            }
        })
        .eraseToAnyPublisher()
    }
    
    func unlikeFeed(feedId: Int) -> AnyPublisher<Bool, APIError> {
        return networkManager.request(
            endpoint: "/feeds/\(feedId)/unlike",
            method: .POST,
            responseType: [String: Bool].self
        )
        .map { response in
            return response["success"] ?? false
        }
        .eraseToAnyPublisher()
    }
    
    func shareFeed(feedId: Int) -> AnyPublisher<Bool, APIError> {
        return networkManager.request(
            endpoint: "/feeds/\(feedId)/share",
            method: .POST,
            responseType: [String: Bool].self
        )
        .map { response in
            return response["success"] ?? false
        }
        .handleEvents(receiveOutput: { [weak self] success in
            if success {
                // 更新分享数
                if let index = self?.cachedFeeds.firstIndex(where: { $0.id == feedId }) {
                    var updatedFeed = self?.cachedFeeds[index]
                    updatedFeed?.sharesCount += 1
                    if let feed = updatedFeed {
                        self?.cachedFeeds[index] = feed
                    }
                }
            }
        })
        .eraseToAnyPublisher()
    }
    
    // MARK: - Comments
    func getFeedComments(feedId: Int, page: Int = 1, limit: Int = 20) -> AnyPublisher<[FeedComment], APIError> {
        return networkManager.request(
            endpoint: "/feeds/\(feedId)/comments?page=\(page)&size=\(limit)",
            method: .GET,
            responseType: FeedCommentListResponse.self
        )
        .map { response in
            return response.comments
        }
        .eraseToAnyPublisher()
    }

    func addComment(feedId: Int, content: String, parentId: Int? = nil) -> AnyPublisher<FeedComment, APIError> {
        struct CommentRequest: Codable {
            let content: String
            let parentId: Int?

            enum CodingKeys: String, CodingKey {
                case content
                case parentId = "parent_id"
            }
        }

        let request = CommentRequest(content: content, parentId: parentId)

        guard let data = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }

        return networkManager.request(
            endpoint: "/feeds/\(feedId)/comments",
            method: .POST,
            body: data,
            responseType: FeedComment.self
        )
        .handleEvents(receiveOutput: { [weak self] _ in
            // 更新评论数
            if let index = self?.cachedFeeds.firstIndex(where: { $0.id == feedId }) {
                var updatedFeed = self?.cachedFeeds[index]
                updatedFeed?.commentsCount += 1
                if let feed = updatedFeed {
                    self?.cachedFeeds[index] = feed
                }
            }
        })
        .eraseToAnyPublisher()
    }

    func deleteComment(commentId: Int) -> AnyPublisher<Bool, APIError> {
        return networkManager.request(
            endpoint: "/feeds/comments/\(commentId)",
            method: .DELETE,
            responseType: EmptyResponse.self
        )
        .map { _ in
            return true
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Cache Management
    func clearCache() {
        cachedFeeds.removeAll()
    }
    
    func refreshFeeds() -> AnyPublisher<[Feed], APIError> {
        return getFeeds(page: 1, limit: 20)
    }
}
