import SwiftUI
import Combine


struct ContentView: View {
    @State private var selectedTab = 0
    @StateObject private var feedStore = FeedStore()
    @StateObject private var petStore = PetStore()
    @StateObject private var interactionStore = InteractionStore()
    @StateObject private var authService = AuthService.shared

    var body: some View {
        TabView(selection: $selectedTab) {
            // Feed页面
            FeedView()
                .environmentObject(feedStore)
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                    Text("动态")
                }
                .tag(0)

            // Profile页面
            ProfileView()
                .environmentObject(petStore)
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "pawprint.fill" : "pawprint")
                    Text("档案")
                }
                .tag(1)

            // Interaction页面
            InteractionView()
                .environmentObject(interactionStore)
                .environmentObject(petStore)
                .tabItem {
                    Image(systemName: selectedTab == 2 ? "message.fill" : "message")
                    Text("聊天")
                }
                .tag(2)

            // 我的页面
            MyProfileView()
                .environmentObject(authService)
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "person.fill" : "person")
                    Text("我的")
                }
                .tag(3)
        }
        .accentColor(Color(hex: "101618"))
    }
}



// MARK: - 简化版我的页面
struct MyProfileView: View {
    @EnvironmentObject var authService: AuthService
    @State private var showingLoginAlert = false
    @State private var username = ""
    @State private var password = ""
    @State private var cancellables = Set<AnyCancellable>()

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if authService.isAuthenticated {
                    // 已登录状态
                    loggedInView
                } else {
                    // 未登录状态
                    notLoggedInView
                }
            }
            .navigationTitle("我的")
            .padding()
        }
        .alert("登录", isPresented: $showingLoginAlert) {
            TextField("用户名", text: $username)
            SecureField("密码", text: $password)
            Button("登录") {
                authService.login(username: username, password: password)
                    .sink(
                        receiveCompletion: { completion in
                            if case .failure(let error) = completion {
                                print("登录失败: \(error)")
                            }
                        },
                        receiveValue: { response in
                            print("登录成功")
                        }
                    )
                    .store(in: &cancellables)
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("请输入您的用户名和密码")
        }
    }

    private var loggedInView: some View {
        VStack(spacing: 20) {
            // 用户头像和信息
            VStack(spacing: 12) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(Color(hex: "5c7d8a"))

                Text(authService.currentUser?.username ?? "用户")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("ID: \(authService.currentUser?.id ?? 0)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 统计信息
            HStack(spacing: 30) {
                statItem(title: "宠物", value: "\(authService.currentUser?.petCount ?? 0)")
                statItem(title: "动态", value: "\(authService.currentUser?.feedCount ?? 0)")
                statItem(title: "聊天", value: "\(authService.currentUser?.chatCount ?? 0)")
            }
            .padding()
            .background(Color(hex: "f8f9fa"))
            .cornerRadius(12)

            Spacer()

            // 退出登录按钮
            Button(action: {
                authService.logout()
                    .sink(
                        receiveCompletion: { completion in
                            if case .failure(let error) = completion {
                                print("登出失败: \(error)")
                            }
                        },
                        receiveValue: { response in
                            print("登出成功")
                        }
                    )
                    .store(in: &cancellables)
            }) {
                Text("退出登录")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.red)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color(hex: "f8f9fa"))
                    .cornerRadius(25)
            }
        }
    }

    private var notLoggedInView: some View {
        VStack(spacing: 30) {
            Spacer()

            Image(systemName: "person.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(Color(hex: "5c7d8a"))

            VStack(spacing: 12) {
                Text("欢迎来到毛孩子AI")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("登录后可以创建专属宠物档案\n与AI宠物进行个性化聊天")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: {
                showingLoginAlert = true
            }) {
                Text("登录")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color(hex: "101618"))
                    .cornerRadius(25)
            }

            Spacer()
        }
    }

    private func statItem(title: String, value: String) -> some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(Color(hex: "101618"))

            Text(title)
                .font(.system(size: 12))
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    ContentView()
}